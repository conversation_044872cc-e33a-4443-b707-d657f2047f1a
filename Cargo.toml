[package]
name = "untitled1"
version = "0.1.0"
edition = "2024"

[dependencies]
chrono = "0.4.41"
cpal = "0.15.3"
dirs = "6.0.0"
display-info = "0.5.4"
eframe = "0.31.1"
egui = "0.31.1"
egui_extras = "0.31.1"
env_logger = "0.11.8"
ffmpeg-next = "7.1.0"
global-hotkey = "0.7.0"
image = "0.25.6"
rfd = "0.15.3"
rodio = "0.20.1"
screenshots = "0.8.10"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
tokio = "1.45.1"
winapi = { version = "0.3.9", features = ["winuser", "wingdi", "wincon"] }
