use display_info::DisplayInfo;
use crate::region_selector::{Point, Rectangle};
use std::error::Error;

#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct Monitor {
    pub id: u32,
    pub name: String,
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
    pub scale_factor: f64,
    pub is_primary: bool,
}

impl Monitor {
    pub fn bounds(&self) -> Rectangle {
        Rectangle {
            x: self.x,
            y: self.y,
            width: self.width,
            height: self.height,
        }
    }
    
    pub fn contains_point(&self, point: Point) -> bool {
        point.x >= self.x 
            && point.x < self.x + self.width as i32
            && point.y >= self.y 
            && point.y < self.y + self.height as i32
    }
}

pub struct MultiMonitorManager {
    monitors: Vec<Monitor>,
}

impl MultiMonitorManager {
    pub fn new() -> Result<Self, Box<dyn Error>> {
        let displays = DisplayInfo::all()?;
        let mut monitors = Vec::new();
        
        for (id, display) in displays.iter().enumerate() {
            monitors.push(Monitor {
                id: id as u32,
                name: format!("Monitor {}", id + 1),
                x: display.x,
                y: display.y,
                width: display.width,
                height: display.height,
                scale_factor: display.scale_factor,
                is_primary: display.is_primary,
            });
        }
        
        // Sort monitors by position (left to right, top to bottom)
        monitors.sort_by(|a, b| {
            if a.y == b.y {
                a.x.cmp(&b.x)
            } else {
                a.y.cmp(&b.y)
            }
        });
        
        Ok(Self { monitors })
    }
    
    pub fn get_monitors(&self) -> &[Monitor] {
        &self.monitors
    }
    
    pub fn get_primary_monitor(&self) -> Option<&Monitor> {
        self.monitors.iter().find(|m| m.is_primary)
    }
    
    pub fn get_monitor_at_point(&self, point: Point) -> Option<&Monitor> {
        self.monitors.iter().find(|m| m.contains_point(point))
    }
    
    pub fn get_virtual_screen_bounds(&self) -> Rectangle {
        if self.monitors.is_empty() {
            return Rectangle { x: 0, y: 0, width: 1920, height: 1080 };
        }
        
        let min_x = self.monitors.iter().map(|m| m.x).min().unwrap_or(0);
        let min_y = self.monitors.iter().map(|m| m.y).min().unwrap_or(0);
        let max_x = self.monitors.iter().map(|m| m.x + m.width as i32).max().unwrap_or(1920);
        let max_y = self.monitors.iter().map(|m| m.y + m.height as i32).max().unwrap_or(1080);
        
        Rectangle {
            x: min_x,
            y: min_y,
            width: (max_x - min_x) as u32,
            height: (max_y - min_y) as u32,
        }
    }
    
    pub fn get_monitor_by_id(&self, id: u32) -> Option<&Monitor> {
        self.monitors.iter().find(|m| m.id == id)
    }
    
    pub fn refresh(&mut self) -> Result<(), Box<dyn Error>> {
        *self = Self::new()?;
        Ok(())
    }
}

impl Default for MultiMonitorManager {
    fn default() -> Self {
        Self::new().unwrap_or_else(|_| Self {
            monitors: vec![Monitor {
                id: 0,
                name: "Default Monitor".to_string(),
                x: 0,
                y: 0,
                width: 1920,
                height: 1080,
                scale_factor: 1.0,
                is_primary: true,
            }],
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_monitor_contains_point() {
        let monitor = Monitor {
            id: 0,
            name: "Test".to_string(),
            x: 100,
            y: 100,
            width: 800,
            height: 600,
            scale_factor: 1.0,
            is_primary: true,
        };
        
        assert!(monitor.contains_point(Point { x: 150, y: 150 }));
        assert!(monitor.contains_point(Point { x: 100, y: 100 }));
        assert!(!monitor.contains_point(Point { x: 50, y: 50 }));
        assert!(!monitor.contains_point(Point { x: 1000, y: 800 }));
    }
    
    #[test]
    fn test_virtual_screen_bounds() {
        let monitors = vec![
            Monitor {
                id: 0,
                name: "Monitor 1".to_string(),
                x: 0,
                y: 0,
                width: 1920,
                height: 1080,
                scale_factor: 1.0,
                is_primary: true,
            },
            Monitor {
                id: 1,
                name: "Monitor 2".to_string(),
                x: 1920,
                y: 0,
                width: 1920,
                height: 1080,
                scale_factor: 1.0,
                is_primary: false,
            },
        ];
        
        let manager = MultiMonitorManager { monitors };
        let bounds = manager.get_virtual_screen_bounds();
        
        assert_eq!(bounds.x, 0);
        assert_eq!(bounds.y, 0);
        assert_eq!(bounds.width, 3840);
        assert_eq!(bounds.height, 1080);
    }
}
