use std::sync::{<PERSON>, <PERSON>tex};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Point {
    pub x: i32,
    pub y: i32,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Rectangle {
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
}

impl Rectangle {
    pub fn new(start: Point, end: Point) -> Self {
        let x = start.x.min(end.x);
        let y = start.y.min(end.y);
        let width = (start.x - end.x).abs() as u32;
        let height = (start.y - end.y).abs() as u32;
        
        Self { x, y, width, height }
    }
    
    pub fn is_valid(&self) -> bool {
        self.width > 10 && self.height > 10
    }
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub enum SelectionState {
    Idle,
    Selecting { start: Point },
    Selected { region: Rectangle },
}

pub struct RegionSelector {
    pub state: Arc<Mutex<SelectionState>>,
    pub is_active: Arc<Mutex<bool>>,
}

impl RegionSelector {
    pub fn new() -> Self {
        Self {
            state: Arc::new(Mutex::new(SelectionState::Idle)),
            is_active: Arc::new(Mutex::new(false)),
        }
    }
    
    pub fn start_selection(&self) {
        *self.is_active.lock().unwrap() = true;
        *self.state.lock().unwrap() = SelectionState::Idle;
    }
    
    pub fn stop_selection(&self) {
        *self.is_active.lock().unwrap() = false;
        *self.state.lock().unwrap() = SelectionState::Idle;
    }
    
    pub fn handle_mouse_down(&self, point: Point) {
        if *self.is_active.lock().unwrap() {
            *self.state.lock().unwrap() = SelectionState::Selecting { start: point };
        }
    }
    
    pub fn handle_mouse_move(&self, point: Point) {
        if let Ok(mut state) = self.state.lock() {
            if let SelectionState::Selecting { start } = *state {
                // Update preview rectangle during selection
                let region = Rectangle::new(start, point);
                if region.is_valid() {
                    *state = SelectionState::Selected { region };
                }
            }
        }
    }
    
    pub fn handle_mouse_up(&self, point: Point) -> Option<Rectangle> {
        if let Ok(mut state) = self.state.lock() {
            if let SelectionState::Selecting { start } = *state {
                let region = Rectangle::new(start, point);
                if region.is_valid() {
                    *state = SelectionState::Selected { region };
                    *self.is_active.lock().unwrap() = false;
                    return Some(region);
                }
            }
        }
        None
    }
    
    pub fn get_selected_region(&self) -> Option<Rectangle> {
        if let Ok(state) = self.state.lock() {
            if let SelectionState::Selected { region } = *state {
                return Some(region);
            }
        }
        None
    }
    
    pub fn clear_selection(&self) {
        *self.state.lock().unwrap() = SelectionState::Idle;
    }
}
