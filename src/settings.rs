use serde::{Deserialize, Serialize};
use std::path::PathBuf;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AppSettings {
    pub download_location: PathBuf,
    pub video_quality: VideoQuality,
    pub video_format: VideoFormat,
    pub frame_rate: u32,
    pub show_cursor: bool,
    pub audio_enabled: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub enum VideoQuality {
    Low,
    Medium,
    High,
    Ultra,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum VideoFormat {
    Mp4,
    Avi,
    Webm,
}

impl Default for AppSettings {
    fn default() -> Self {
        Self {
            download_location: dirs::desktop_dir().unwrap_or_else(|| PathBuf::from(".")),
            video_quality: VideoQuality::Medium,
            video_format: VideoFormat::Mp4,
            frame_rate: 30,
            show_cursor: true,
            audio_enabled: false,
        }
    }
}

impl AppSettings {
    pub fn load() -> Self {
        let config_dir = dirs::config_dir()
            .unwrap_or_else(|| PathBuf::from("."))
            .join("screen_recorder");

        let config_file = config_dir.join("settings.json");

        if config_file.exists() {
            if let Ok(content) = std::fs::read_to_string(&config_file) {
                if let Ok(settings) = serde_json::from_str(&content) {
                    return settings;
                }
            }
        }

        Self::default()
    }

    pub fn save(&self) -> Result<(), Box<dyn std::error::Error>> {
        let config_dir = dirs::config_dir()
            .unwrap_or_else(|| PathBuf::from("."))
            .join("screen_recorder");

        std::fs::create_dir_all(&config_dir)?;

        let config_file = config_dir.join("settings.json");
        let content = serde_json::to_string_pretty(self)?;
        std::fs::write(config_file, content)?;

        Ok(())
    }
}

impl VideoQuality {
    pub fn to_string(&self) -> &'static str {
        match self {
            VideoQuality::Low => "Low",
            VideoQuality::Medium => "Medium",
            VideoQuality::High => "High",
            VideoQuality::Ultra => "Ultra",
        }
    }

    pub fn bitrate(&self) -> u32 {
        match self {
            VideoQuality::Low => 1000,
            VideoQuality::Medium => 2500,
            VideoQuality::High => 5000,
            VideoQuality::Ultra => 10000,
        }
    }
}

impl VideoFormat {
    pub fn to_string(&self) -> &'static str {
        match self {
            VideoFormat::Mp4 => "MP4",
            VideoFormat::Avi => "AVI",
            VideoFormat::Webm => "WebM",
        }
    }

    pub fn extension(&self) -> &'static str {
        match self {
            VideoFormat::Mp4 => "mp4",
            VideoFormat::Avi => "avi",
            VideoFormat::Webm => "webm",
        }
    }
}
