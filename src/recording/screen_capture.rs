use crate::region_selector::Rectangle;
use image::{ImageBuffer, RgbaImage};
use screenshots::Screen;
use std::error::Error;

pub fn capture_region(region: Rectangle) -> Result<RgbaImage, Box<dyn Error>> {
    // Get all screens
    let screens = Screen::all()?;

    // For now, use the primary screen
    // TODO: Handle multi-monitor setups properly
    if let Some(screen) = screens.first() {
        // Capture the entire screen first
        let screenshot = screen.capture()?;

        // Convert to image format
        let img = ImageBuffer::from_raw(
            screenshot.width() as u32,
            screenshot.height() as u32,
            screenshot.as_raw().to_vec(),
        ).ok_or("Failed to create image buffer")?;

        // Crop to the specified region
        let cropped = crop_image(&img, region)?;

        Ok(cropped)
    } else {
        Err("No screens found".into())
    }
}

fn crop_image(img: &RgbaImage, region: Rectangle) -> Result<RgbaImage, Box<dyn Error>> {
    let img_width = img.width() as i32;
    let img_height = img.height() as i32;

    // Ensure the region is within screen bounds
    let x = region.x.max(0).min(img_width - 1);
    let y = region.y.max(0).min(img_height - 1);
    let width = region.width.min((img_width - x) as u32);
    let height = region.height.min((img_height - y) as u32);

    if width == 0 || height == 0 {
        return Err("Invalid crop region".into());
    }

    let mut cropped = ImageBuffer::new(width, height);

    for crop_y in 0..height {
        for crop_x in 0..width {
            let src_x = x + crop_x as i32;
            let src_y = y + crop_y as i32;

            if src_x >= 0 && src_x < img_width && src_y >= 0 && src_y < img_height {
                let pixel = img.get_pixel(src_x as u32, src_y as u32);
                cropped.put_pixel(crop_x, crop_y, *pixel);
            }
        }
    }

    Ok(cropped)
}

pub fn get_screen_dimensions() -> Result<(u32, u32), Box<dyn Error>> {
    let screens = Screen::all()?;

    if let Some(screen) = screens.first() {
        Ok((screen.display_info.width as u32, screen.display_info.height as u32))
    } else {
        Err("No screens found".into())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_screen_capture() {
        let region = Rectangle {
            x: 100,
            y: 100,
            width: 200,
            height: 200,
        };

        match capture_region(region) {
            Ok(img) => {
                assert_eq!(img.width(), 200);
                assert_eq!(img.height(), 200);
            }
            Err(e) => {
                println!("Screen capture test failed: {}", e);
                // Don't fail the test as this might not work in all environments
            }
        }
    }
}
