pub mod screen_capture;
pub mod video_encoder;

use crate::region_selector::Rectangle;
use crate::settings::AppSettings;
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::{Duration, Instant};
use std::sync::mpsc;

#[derive(Debug, <PERSON>lone)]
pub enum RecordingState {
    Idle,
    Recording,
    Paused,
    Stopping,
}

pub struct ScreenRecorder {
    pub state: Arc<Mutex<RecordingState>>,
    pub region: Option<Rectangle>,
    pub settings: AppSettings,
    stop_sender: Option<mpsc::Sender<()>>,
}

impl ScreenRecorder {
    pub fn new(settings: AppSettings) -> Self {
        Self {
            state: Arc::new(Mutex::new(RecordingState::Idle)),
            region: None,
            settings,
            stop_sender: None,
        }
    }

    pub fn set_region(&mut self, region: Rectangle) {
        self.region = Some(region);
    }

    pub fn start_recording(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(region) = self.region {
            *self.state.lock().unwrap() = RecordingState::Recording;

            let (stop_tx, stop_rx) = mpsc::channel();
            self.stop_sender = Some(stop_tx);

            let state = Arc::clone(&self.state);
            let settings = self.settings.clone();

            // Start recording in a separate thread
            thread::spawn(move || {
                let mut frame_count = 0;
                let frame_duration = Duration::from_millis(1000 / settings.frame_rate as u64);
                let mut last_frame_time = Instant::now();

                loop {
                    // Check if we should stop
                    if stop_rx.try_recv().is_ok() {
                        break;
                    }

                    // Check if we're still recording
                    if let Ok(current_state) = state.lock() {
                        match *current_state {
                            RecordingState::Recording => {},
                            _ => break,
                        }
                    }

                    // Capture frame at the specified frame rate
                    if last_frame_time.elapsed() >= frame_duration {
                        if let Ok(screenshot) = screen_capture::capture_region(region) {
                            // TODO: Add frame to video encoder
                            frame_count += 1;
                            println!("Captured frame {}", frame_count);
                        }
                        last_frame_time = Instant::now();
                    }

                    // Small sleep to prevent excessive CPU usage
                    thread::sleep(Duration::from_millis(1));
                }

                // Set state to idle when recording stops
                *state.lock().unwrap() = RecordingState::Idle;
                println!("Recording stopped. Total frames: {}", frame_count);
            });

            Ok(())
        } else {
            Err("No region selected for recording".into())
        }
    }

    pub fn stop_recording(&mut self) {
        *self.state.lock().unwrap() = RecordingState::Stopping;

        if let Some(sender) = self.stop_sender.take() {
            let _ = sender.send(());
        }
    }

    pub fn pause_recording(&self) {
        *self.state.lock().unwrap() = RecordingState::Paused;
    }

    pub fn resume_recording(&self) {
        *self.state.lock().unwrap() = RecordingState::Recording;
    }

    pub fn is_recording(&self) -> bool {
        if let Ok(state) = self.state.lock() {
            matches!(*state, RecordingState::Recording)
        } else {
            false
        }
    }

    pub fn get_state(&self) -> RecordingState {
        self.state.lock().unwrap().clone()
    }
}
