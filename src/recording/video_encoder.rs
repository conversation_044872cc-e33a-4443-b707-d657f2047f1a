use crate::settings::{AppSettings, VideoFormat};
use image::RgbaImage;
use std::error::Error;
use std::path::PathBuf;
use chrono::Utc;

pub struct VideoEncoder {
    output_path: PathBuf,
    settings: AppSettings,
    frames: Vec<RgbaImage>,
    is_recording: bool,
}

impl VideoEncoder {
    pub fn new(settings: AppSettings) -> Self {
        let timestamp = Utc::now().format("%Y%m%d_%H%M%S");
        let filename = format!("screen_recording_{}.{}", timestamp, settings.video_format.extension());
        let output_path = settings.download_location.join(filename);
        
        Self {
            output_path,
            settings,
            frames: Vec::new(),
            is_recording: false,
        }
    }
    
    pub fn start_recording(&mut self) -> Result<(), Box<dyn Error>> {
        self.is_recording = true;
        self.frames.clear();
        
        // Ensure output directory exists
        if let Some(parent) = self.output_path.parent() {
            std::fs::create_dir_all(parent)?;
        }
        
        println!("Started recording to: {:?}", self.output_path);
        Ok(())
    }
    
    pub fn add_frame(&mut self, frame: RgbaImage) -> Result<(), Box<dyn Error>> {
        if self.is_recording {
            self.frames.push(frame);
        }
        Ok(())
    }
    
    pub fn stop_recording(&mut self) -> Result<PathBuf, Box<dyn Error>> {
        self.is_recording = false;
        
        if self.frames.is_empty() {
            return Err("No frames to encode".into());
        }
        
        // For now, we'll save frames as individual images
        // In a full implementation, you would use a proper video encoding library
        self.save_as_gif()?;
        
        let output_path = self.output_path.clone();
        self.frames.clear();
        
        Ok(output_path)
    }
    
    fn save_as_gif(&self) -> Result<(), Box<dyn Error>> {
        if self.frames.is_empty() {
            return Err("No frames to save".into());
        }
        
        // Change extension to gif for now
        let mut gif_path = self.output_path.clone();
        gif_path.set_extension("gif");
        
        // For demonstration, save the first frame as a static image
        // In a real implementation, you would create an animated GIF or proper video
        if let Some(first_frame) = self.frames.first() {
            first_frame.save(&gif_path)?;
            println!("Saved recording as: {:?}", gif_path);
        }
        
        Ok(())
    }
    
    pub fn get_frame_count(&self) -> usize {
        self.frames.len()
    }
    
    pub fn get_output_path(&self) -> &PathBuf {
        &self.output_path
    }
    
    pub fn is_recording(&self) -> bool {
        self.is_recording
    }
}

// TODO: Implement proper video encoding using FFmpeg or similar
// This would require additional dependencies like ffmpeg-next or opencv
pub fn encode_frames_to_video(
    frames: &[RgbaImage],
    output_path: &PathBuf,
    settings: &AppSettings,
) -> Result<(), Box<dyn Error>> {
    match settings.video_format {
        VideoFormat::Mp4 => encode_to_mp4(frames, output_path, settings),
        VideoFormat::Avi => encode_to_avi(frames, output_path, settings),
        VideoFormat::Webm => encode_to_webm(frames, output_path, settings),
    }
}

fn encode_to_mp4(
    _frames: &[RgbaImage],
    _output_path: &PathBuf,
    _settings: &AppSettings,
) -> Result<(), Box<dyn Error>> {
    // TODO: Implement MP4 encoding
    Err("MP4 encoding not yet implemented".into())
}

fn encode_to_avi(
    _frames: &[RgbaImage],
    _output_path: &PathBuf,
    _settings: &AppSettings,
) -> Result<(), Box<dyn Error>> {
    // TODO: Implement AVI encoding
    Err("AVI encoding not yet implemented".into())
}

fn encode_to_webm(
    _frames: &[RgbaImage],
    _output_path: &PathBuf,
    _settings: &AppSettings,
) -> Result<(), Box<dyn Error>> {
    // TODO: Implement WebM encoding
    Err("WebM encoding not yet implemented".into())
}
