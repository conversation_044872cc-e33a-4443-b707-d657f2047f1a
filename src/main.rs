mod settings;
mod region_selector;
mod recording;
mod gui;
mod overlay_window;
mod hotkeys;
mod audio;
mod multi_monitor;

use eframe::egui;
use gui::{AppState, main_window};
use std::sync::{Arc, Mutex};

fn main() -> Result<(), eframe::Error> {
    env_logger::init(); // Log to stderr (if you run with `RUST_LOG=debug`).

    let app_state = Arc::new(Mutex::new(AppState::new()));

    // Initialize hotkeys
    if let Err(e) = hotkeys::init_hotkeys(Arc::clone(&app_state)) {
        eprintln!("Failed to initialize hotkeys: {}", e);
    }

    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([800.0, 600.0])
            .with_title("Screen Region Recorder")
            .with_icon(
                // Add an icon if you have one
                eframe::icon_data::from_png_bytes(&[]).unwrap_or_default(),
            ),
        ..Default::default()
    };

    eframe::run_native(
        "Screen Region Recorder",
        options,
        Box::new(move |_cc| {
            // This gives us image support:
            egui_extras::install_image_loaders(&_cc.egui_ctx);

            Ok(Box::new(ScreenRecorderApp::new(Arc::clone(&app_state))))
        }),
    )
}

struct ScreenRecorderApp {
    app_state: Arc<Mutex<AppState>>,
}

impl ScreenRecorderApp {
    fn new(app_state: Arc<Mutex<AppState>>) -> Self {
        Self { app_state }
    }
}

impl eframe::App for ScreenRecorderApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        if let Ok(mut app_state) = self.app_state.lock() {
            // Show main window
            main_window::show_main_window(ctx, &mut app_state);

            // Handle region selection trigger
            if app_state.should_start_selection {
                app_state.should_start_selection = false;
                let app_state_clone = Arc::clone(&self.app_state);
                std::thread::spawn(move || {
                    overlay_window::show_selection_overlay(app_state_clone);
                });
            }
        }

        // Request repaint for smooth animations
        ctx.request_repaint();
    }

    fn on_exit(&mut self, _gl: Option<&eframe::glow::Context>) {
        // Save settings on exit
        if let Ok(app_state) = self.app_state.lock() {
            app_state.save_settings();
        }
    }
}
