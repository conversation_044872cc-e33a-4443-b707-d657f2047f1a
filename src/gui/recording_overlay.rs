use crate::gui::AppState;
use crate::region_selector::{SelectionState, Point};
use egui::{Context, Area, Frame, Color32, Stroke, Rect, Pos2, Vec2};

pub fn show_recording_overlay(ctx: &Context, app_state: &mut AppState) {
    if !app_state.show_overlay {
        return;
    }

    // Create a full-screen overlay
    Area::new("recording_overlay".into())
        .fixed_pos(Pos2::ZERO)
        .show(ctx, |ui| {
            // Get screen size
            let screen_rect = ctx.screen_rect();

            // Semi-transparent background
            ui.allocate_ui_with_layout(
                screen_rect.size(),
                egui::Layout::top_down(egui::Align::LEFT),
                |ui| {
                    // Draw selection rectangle if active
                    draw_selection_overlay(ui, app_state, screen_rect);

                    // Handle mouse input for region selection
                    handle_selection_input(ui, app_state);

                    // Show instructions
                    show_selection_instructions(ui, screen_rect);
                },
            );
        });

    // Show recording controls if a region is selected and recording
    if let Some(region) = app_state.region_selector.get_selected_region() {
        let recorder = app_state.recorder.clone();
        let is_recording = if let Ok(recorder) = recorder.lock() {
            recorder.is_recording()
        } else {
            false
        };

        if is_recording {
            show_recording_controls(ctx, app_state, region);
        }
    }
}

fn draw_selection_overlay(ui: &mut egui::Ui, app_state: &AppState, screen_rect: Rect) {
    let painter = ui.painter();

    // Draw semi-transparent overlay
    painter.rect_filled(
        screen_rect,
        0.0,
        Color32::from_black_alpha(100),
    );

    // Draw selection rectangle if in progress
    if let Ok(state) = app_state.region_selector.state.lock() {
        match *state {
            SelectionState::Selecting { start } => {
                if let Some(pointer_pos) = ui.ctx().pointer_latest_pos() {
                    let selection_rect = Rect::from_two_pos(
                        Pos2::new(start.x as f32, start.y as f32),
                        pointer_pos,
                    );

                    // Clear the selected area
                    painter.rect_filled(selection_rect, 0.0, Color32::TRANSPARENT);

                    // Draw selection border
                    painter.rect_stroke(
                        selection_rect,
                        0.0,
                        Stroke::new(2.0, Color32::from_rgb(255, 255, 0)),
                        egui::epaint::StrokeKind::Outside,
                    );
                }
            }
            SelectionState::Selected { region } => {
                let selection_rect = Rect::from_min_size(
                    Pos2::new(region.x as f32, region.y as f32),
                    Vec2::new(region.width as f32, region.height as f32),
                );

                // Clear the selected area
                painter.rect_filled(selection_rect, 0.0, Color32::TRANSPARENT);

                // Draw selection border
                painter.rect_stroke(
                    selection_rect,
                    0.0,
                    Stroke::new(3.0, Color32::from_rgb(0, 255, 0)),
                    egui::epaint::StrokeKind::Outside,
                );

                // Draw corner handles
                draw_corner_handles(&painter, selection_rect);
            }
            _ => {}
        }
    }
}

fn draw_corner_handles(painter: &egui::Painter, rect: Rect) {
    let handle_size = 8.0;
    let handle_color = Color32::from_rgb(0, 255, 0);

    let corners = [
        rect.min,
        Pos2::new(rect.max.x, rect.min.y),
        rect.max,
        Pos2::new(rect.min.x, rect.max.y),
    ];

    for corner in corners {
        painter.rect_filled(
            Rect::from_center_size(corner, Vec2::splat(handle_size)),
            0.0,
            handle_color,
        );
    }
}

fn handle_selection_input(ui: &mut egui::Ui, app_state: &mut AppState) {
    let response = ui.allocate_response(ui.available_size(), egui::Sense::click_and_drag());

    if response.clicked() {
        if let Some(pos) = response.interact_pointer_pos() {
            let point = Point {
                x: pos.x as i32,
                y: pos.y as i32,
            };
            app_state.region_selector.handle_mouse_down(point);
        }
    }

    if response.dragged() {
        if let Some(pos) = response.interact_pointer_pos() {
            let point = Point {
                x: pos.x as i32,
                y: pos.y as i32,
            };
            app_state.region_selector.handle_mouse_move(point);
        }
    }

    if response.drag_stopped() {
        if let Some(pos) = response.interact_pointer_pos() {
            let point = Point {
                x: pos.x as i32,
                y: pos.y as i32,
            };
            if let Some(_region) = app_state.region_selector.handle_mouse_up(point) {
                app_state.show_overlay = false;
            }
        }
    }

    // Handle escape key to cancel selection
    if ui.input(|i| i.key_pressed(egui::Key::Escape)) {
        app_state.region_selector.stop_selection();
        app_state.show_overlay = false;
    }
}

fn show_selection_instructions(ui: &mut egui::Ui, screen_rect: Rect) {
    // Show instructions at the top of the screen
    Area::new("selection_instructions".into())
        .fixed_pos(Pos2::new(screen_rect.center().x - 200.0, 50.0))
        .show(ui.ctx(), |ui| {
            Frame::popup(ui.style())
                .fill(Color32::from_black_alpha(200))
                .show(ui, |ui| {
                    ui.vertical_centered(|ui| {
                        ui.colored_label(Color32::WHITE, "Select Recording Region");
                        ui.colored_label(Color32::LIGHT_GRAY, "Click and drag to select an area");
                        ui.colored_label(Color32::LIGHT_GRAY, "Press ESC to cancel");
                    });
                });
        });
}

fn show_recording_controls(ctx: &Context, app_state: &mut AppState, region: crate::region_selector::Rectangle) {
    // Position controls below the recording region
    let control_pos = Pos2::new(
        region.x as f32,
        (region.y + region.height as i32 + 10) as f32,
    );

    Area::new("recording_controls".into())
        .fixed_pos(control_pos)
        .show(ctx, |ui| {
            Frame::popup(ui.style())
                .fill(Color32::from_black_alpha(220))
                .show(ui, |ui| {
                    ui.horizontal(|ui| {
                        ui.colored_label(Color32::from_rgb(255, 0, 0), "● REC");

                        ui.separator();

                        if ui.button("⏹ Stop").clicked() {
                            if let Ok(mut recorder) = app_state.recorder.lock() {
                                recorder.stop_recording();
                            }
                        }

                        if ui.button("⏸ Pause").clicked() {
                            if let Ok(recorder) = app_state.recorder.lock() {
                                recorder.pause_recording();
                            }
                        }

                        ui.separator();

                        if ui.button("❌ Cancel").clicked() {
                            if let Ok(mut recorder) = app_state.recorder.lock() {
                                recorder.stop_recording();
                            }
                            app_state.region_selector.clear_selection();
                        }
                    });
                });
        });
}
