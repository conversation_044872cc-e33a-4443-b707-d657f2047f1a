pub mod main_window;
pub mod recording_overlay;

use crate::settings::AppSettings;
use crate::region_selector::RegionSelector;
use crate::recording::ScreenRecorder;
use std::sync::{Arc, Mutex};

pub struct AppState {
    pub settings: AppSettings,
    pub region_selector: RegionSelector,
    pub recorder: Arc<Mutex<ScreenRecorder>>,
    pub show_settings: bool,
    pub show_overlay: bool,
}

impl AppState {
    pub fn new() -> Self {
        let settings = AppSettings::load();
        let recorder = Arc::new(Mutex::new(ScreenRecorder::new(settings.clone())));
        
        Self {
            settings,
            region_selector: RegionSelector::new(),
            recorder,
            show_settings: false,
            show_overlay: false,
        }
    }
    
    pub fn save_settings(&self) {
        if let Err(e) = self.settings.save() {
            eprintln!("Failed to save settings: {}", e);
        }
    }
}
