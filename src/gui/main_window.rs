use crate::gui::AppState;
use crate::settings::{VideoQuality, VideoFormat};
use egui::{Context, CentralPanel, TopBottomPanel, SidePanel, Button, ComboBox};
use std::path::PathBuf;

pub fn show_main_window(ctx: &Context, app_state: &mut AppState) {
    // Top panel with main controls
    TopBottomPanel::top("top_panel").show(ctx, |ui| {
        ui.horizontal(|ui| {
            ui.heading("🎥 Screen Recorder");
            
            ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                if ui.button("⚙ Settings").clicked() {
                    app_state.show_settings = !app_state.show_settings;
                }
                
                if ui.button("❌ Exit").clicked() {
                    ctx.send_viewport_cmd(egui::ViewportCommand::Close);
                }
            });
        });
    });
    
    // Settings panel (side panel)
    if app_state.show_settings {
        SidePanel::right("settings_panel")
            .resizable(true)
            .default_width(300.0)
            .show(ctx, |ui| {
                show_settings_panel(ui, app_state);
            });
    }
    
    // Main content area
    CentralPanel::default().show(ctx, |ui| {
        show_main_content(ui, app_state);
    });
}

fn show_settings_panel(ui: &mut egui::Ui, app_state: &mut AppState) {
    ui.heading("Settings");
    ui.separator();
    
    // Download location
    ui.label("Download Location:");
    ui.horizontal(|ui| {
        let path_str = app_state.settings.download_location.to_string_lossy();
        ui.text_edit_singleline(&mut path_str.to_string());
        
        if ui.button("📁 Browse").clicked() {
            if let Some(path) = rfd::FileDialog::new().pick_folder() {
                app_state.settings.download_location = path;
                app_state.save_settings();
            }
        }
    });
    
    ui.add_space(10.0);
    
    // Video quality
    ui.label("Video Quality:");
    ComboBox::from_label("")
        .selected_text(app_state.settings.video_quality.to_string())
        .show_ui(ui, |ui| {
            ui.selectable_value(&mut app_state.settings.video_quality, VideoQuality::Low, "Low");
            ui.selectable_value(&mut app_state.settings.video_quality, VideoQuality::Medium, "Medium");
            ui.selectable_value(&mut app_state.settings.video_quality, VideoQuality::High, "High");
            ui.selectable_value(&mut app_state.settings.video_quality, VideoQuality::Ultra, "Ultra");
        });
    
    ui.add_space(10.0);
    
    // Video format
    ui.label("Video Format:");
    ComboBox::from_label("")
        .selected_text(app_state.settings.video_format.to_string())
        .show_ui(ui, |ui| {
            ui.selectable_value(&mut app_state.settings.video_format, VideoFormat::Mp4, "MP4");
            ui.selectable_value(&mut app_state.settings.video_format, VideoFormat::Avi, "AVI");
            ui.selectable_value(&mut app_state.settings.video_format, VideoFormat::Webm, "WebM");
        });
    
    ui.add_space(10.0);
    
    // Frame rate
    ui.label("Frame Rate:");
    ui.add(egui::Slider::new(&mut app_state.settings.frame_rate, 10..=60).suffix(" fps"));
    
    ui.add_space(10.0);
    
    // Additional options
    ui.checkbox(&mut app_state.settings.show_cursor, "Show cursor in recording");
    ui.checkbox(&mut app_state.settings.audio_enabled, "Record audio (not implemented)");
    
    ui.add_space(20.0);
    
    if ui.button("💾 Save Settings").clicked() {
        app_state.save_settings();
    }
}

fn show_main_content(ui: &mut egui::Ui, app_state: &mut AppState) {
    ui.vertical_centered(|ui| {
        ui.add_space(50.0);
        
        ui.heading("Screen Region Recorder");
        ui.add_space(20.0);
        
        ui.label("Click 'Select Region' to choose an area of your screen to record.");
        ui.add_space(10.0);
        
        // Main action buttons
        if ui.add_sized([200.0, 40.0], Button::new("🎯 Select Region")).clicked() {
            app_state.region_selector.start_selection();
            app_state.show_overlay = true;
        }
        
        ui.add_space(10.0);
        
        // Show selected region info
        if let Some(region) = app_state.region_selector.get_selected_region() {
            ui.label(format!(
                "Selected region: {}x{} at ({}, {})",
                region.width, region.height, region.x, region.y
            ));
            
            ui.add_space(10.0);
            
            // Recording controls
            let recorder = app_state.recorder.clone();
            let is_recording = if let Ok(recorder) = recorder.lock() {
                recorder.is_recording()
            } else {
                false
            };
            
            if !is_recording {
                if ui.add_sized([200.0, 40.0], Button::new("🔴 Start Recording")).clicked() {
                    if let Ok(mut recorder) = recorder.lock() {
                        recorder.set_region(region);
                        if let Err(e) = recorder.start_recording() {
                            eprintln!("Failed to start recording: {}", e);
                        }
                    }
                }
            } else {
                if ui.add_sized([200.0, 40.0], Button::new("⏹ Stop Recording")).clicked() {
                    if let Ok(mut recorder) = recorder.lock() {
                        recorder.stop_recording();
                    }
                }
            }
            
            ui.add_space(10.0);
            
            if ui.button("🗑 Clear Selection").clicked() {
                app_state.region_selector.clear_selection();
            }
        }
        
        ui.add_space(30.0);
        
        // Instructions
        ui.group(|ui| {
            ui.vertical(|ui| {
                ui.heading("How to use:");
                ui.label("1. Click 'Select Region' to start selection mode");
                ui.label("2. Click and drag on your screen to select the area to record");
                ui.label("3. Click 'Start Recording' to begin recording the selected region");
                ui.label("4. Click 'Stop Recording' when finished");
                ui.label("5. Your video will be saved to the download location");
            });
        });
    });
}
